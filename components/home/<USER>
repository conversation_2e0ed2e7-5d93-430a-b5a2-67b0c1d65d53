<template>
  <section class="contact-section py-16 lg:py-24 bg-[#0a0d12] relative overflow-hidden">
    <div class="container mx-auto px-4 lg:px-8 max-w-4xl">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="font-['Bricolage_Grotesque'] text-3xl lg:text-5xl font-normal leading-tight text-white tracking-tight mb-4">
          Book a Call with Alwi
        </h2>
        <p class="font-['Lato'] text-lg lg:text-xl font-normal leading-relaxed text-[#a3a7ae]">
          Get a deep discuss through your needs and Ideas
        </p>
      </div>

      <!-- Contact Form -->
      <div class="bg-gradient-to-br from-[#1b2335] to-[#0f1621] rounded-3xl p-6 lg:p-8 border border-[rgba(255,255,255,0.05)] relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
          <div class="absolute top-8 right-8 w-32 h-32 border border-[#156fee] rounded-full"></div>
          <div class="absolute bottom-8 left-8 w-24 h-24 bg-[#156fee] rounded-full blur-xl opacity-30"></div>
        </div>

        <form @submit.prevent="submitForm" class="relative z-10 space-y-6">
          <!-- Name and Email Row -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <label for="fullName" class="block font-['Lato'] text-sm font-medium text-[#a3a7ae]">
                Full Name *
              </label>
              <input
                id="fullName"
                v-model="form.fullName"
                type="text"
                required
                class="w-full px-4 py-3 bg-[#0a0d12] border border-[rgba(255,255,255,0.1)] rounded-lg font-['Lato'] text-white placeholder-[#a3a7ae] focus:outline-none focus:border-[#156fee] focus:ring-1 focus:ring-[#156fee] transition-all duration-300"
                placeholder="Enter your full name"
              />
            </div>
            
            <div class="space-y-2">
              <label for="email" class="block font-['Lato'] text-sm font-medium text-[#a3a7ae]">
                Email Address *
              </label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                required
                class="w-full px-4 py-3 bg-[#0a0d12] border border-[rgba(255,255,255,0.1)] rounded-lg font-['Lato'] text-white placeholder-[#a3a7ae] focus:outline-none focus:border-[#156fee] focus:ring-1 focus:ring-[#156fee] transition-all duration-300"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <!-- Service Type and Budget Row -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <label for="serviceType" class="block font-['Lato'] text-sm font-medium text-[#a3a7ae]">
                Service Type *
              </label>
              <select
                id="serviceType"
                v-model="form.serviceType"
                required
                class="w-full px-4 py-3 bg-[#0a0d12] border border-[rgba(255,255,255,0.1)] rounded-lg font-['Lato'] text-white focus:outline-none focus:border-[#156fee] focus:ring-1 focus:ring-[#156fee] transition-all duration-300"
              >
                <option value="">Please Select</option>
                <option value="consulting">Technical Consulting</option>
                <option value="development">Full-Stack Development</option>
                <option value="hiring">Talent Acquisition</option>
                <option value="mentoring">Team Mentoring</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div class="space-y-2">
              <label for="budget" class="block font-['Lato'] text-sm font-medium text-[#a3a7ae]">
                Budget Range
              </label>
              <select
                id="budget"
                v-model="form.budget"
                class="w-full px-4 py-3 bg-[#0a0d12] border border-[rgba(255,255,255,0.1)] rounded-lg font-['Lato'] text-white focus:outline-none focus:border-[#156fee] focus:ring-1 focus:ring-[#156fee] transition-all duration-300"
              >
                <option value="">Please Select</option>
                <option value="5k-10k">$5K - $10K</option>
                <option value="10k-25k">$10K - $25K</option>
                <option value="25k-50k">$25K - $50K</option>
                <option value="50k+">$50K+</option>
              </select>
            </div>
          </div>

          <!-- Project Description -->
          <div class="space-y-2">
            <label for="description" class="block font-['Lato'] text-sm font-medium text-[#a3a7ae]">
              Project Description *
            </label>
            <textarea
              id="description"
              v-model="form.description"
              required
              rows="4"
              class="w-full px-4 py-3 bg-[#0a0d12] border border-[rgba(255,255,255,0.1)] rounded-lg font-['Lato'] text-white placeholder-[#a3a7ae] focus:outline-none focus:border-[#156fee] focus:ring-1 focus:ring-[#156fee] transition-all duration-300 resize-none"
              placeholder="Tell us about your project, goals, and how we can help..."
            ></textarea>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-center pt-4">
            <button
              type="submit"
              :disabled="isSubmitting"
              class="bg-gradient-to-r from-[#156fee] to-[#0d4bb8] hover:from-[#0d4bb8] hover:to-[#156fee] px-8 py-4 rounded-full font-['Lato'] text-white font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-[#156fee]/25 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3"
            >
              <span>{{ isSubmitting ? 'Sending...' : 'Send Message' }}</span>
              <svg v-if="!isSubmitting" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
              <div v-else class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Background Decorations -->
    <div class="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-[#156fee] to-transparent rounded-full opacity-10 blur-3xl"></div>
    <div class="absolute bottom-20 right-20 w-40 h-40 bg-gradient-to-tl from-[#156fee] to-transparent rounded-full opacity-10 blur-3xl"></div>
  </section>
</template>

<script setup>
import { ref, reactive } from 'vue';

const isSubmitting = ref(false);

const form = reactive({
  fullName: '',
  email: '',
  serviceType: '',
  budget: '',
  description: ''
});

const submitForm = async () => {
  isSubmitting.value = true;
  
  try {
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Reset form after successful submission
    Object.keys(form).forEach(key => {
      form[key] = '';
    });
    
    // You can add success notification here
    console.log('Form submitted successfully!');
    
  } catch (error) {
    console.error('Error submitting form:', error);
    // You can add error notification here
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
/* Form animations */
.contact-section {
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Input focus effects */
input:focus,
textarea:focus,
select:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(21, 111, 238, 0.15);
}

/* Button hover effects */
button:hover:not(:disabled) {
  transform: translateY(-2px);
}

/* Custom scrollbar for textarea */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb {
  background: #156fee;
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: #0d4bb8;
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .contact-form {
    padding: 1.5rem;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
}
</style>