<template>
  <div
      class="flex w-[1600px] pt-[120px] pr-[120px] pb-[120px] pl-[120px] flex-col gap-[64px] justify-center items-center shrink-0 flex-nowrap relative overflow-hidden z-[74] bg-[#0a0d12]"
  >
    <div
        class="flex flex-col gap-[64px] items-start self-stretch shrink-0 flex-nowrap relative z-[75]"
    >
      <div
          class="flex flex-col gap-[16px] items-center self-stretch shrink-0 flex-nowrap relative z-[76]"
      >
          <span
              class="flex w-[465px] h-[90px] justify-center items-center shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[72px] font-normal leading-[90px] text-[#fff] tracking-[-1.44px] relative text-center whitespace-nowrap z-[77]"
          >Hiring Positions</span
          ><span
          class="h-[24px] self-stretch shrink-0 basis-auto font-['Lato'] text-[20px] font-normal leading-[24px] text-[#a3a7ae] relative text-center whitespace-nowrap z-[78]"
      >Explore the testimonials and feedback from our valued customers to
            gain insights into their
          </span>
      </div>
      <div
          class="flex flex-col gap-[64px] items-end self-stretch shrink-0 flex-nowrap relative z-[79]"
      >
        <div
            class="flex gap-[32px] items-center self-stretch shrink-0 flex-nowrap relative z-[80]"
        >
          <div
              class="w-[434px] h-[503.98px] shrink-0 bg-[#1b2335] rounded-[24px] relative z-[81]"
          >
            <div
                class="flex w-[324px] flex-col gap-[24px] items-start flex-nowrap relative z-[82] mt-[124px] mr-0 mb-0 ml-[52px]"
            >
              <div
                  class="w-[76px] h-[76px] shrink-0 bg-[url(https://static.codia.ai/image/2025-03-24/5db2295a-98d9-4f8c-9afd-b38953bcba33.svg)] bg-cover bg-no-repeat relative overflow-hidden z-[83]"
              ></div>
              <span
                  class="h-[44px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[36px] font-bold leading-[44px] text-[#fff] tracking-[-0.72px] relative text-left whitespace-nowrap z-[84]"
              >Fullstack Developer</span
              ><span
                class="flex w-[310.922px] h-[79.97px] justify-start items-center shrink-0 font-['Lato'] text-[20px] font-normal leading-[24px] text-[#d5d6d9] relative text-left z-[85]"
            >Duis aute irure dolor in<br />reprehenderit in
                  voluptate.</span
            >
              <div
                  class="w-[56px] h-[6px] shrink-0 bg-[#fff] relative z-[86]"
              ></div>
            </div>
          </div>
          <div
              class="w-[434px] h-[503.98px] shrink-0 bg-[#1b2335] rounded-[24px] relative z-[87]"
          >
            <div
                class="flex w-[299.704px] flex-col gap-[24px] items-start flex-nowrap relative z-[88] mt-[124px] mr-0 mb-0 ml-[52px]"
            >
              <div
                  class="w-[76px] h-[76px] shrink-0 bg-[url(https://static.codia.ai/image/2025-03-24/474d4686-cc3b-4c4a-9bf6-09c1335cfae6.svg)] bg-cover bg-no-repeat relative overflow-hidden z-[89]"
              ></div>
              <span
                  class="h-[46.02px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[36px] font-bold leading-[44px] text-[#fff] tracking-[-0.72px] relative text-left whitespace-nowrap z-[90]"
              >DevOps</span
              ><span
                class="flex w-[299.704px] h-[79.97px] justify-start items-center self-stretch shrink-0 font-['Lato'] text-[20px] font-normal leading-[24px] text-[#d5d6d9] relative text-left z-[91]"
            >Ut enim ad minim veniam,<br />quis nostrud
                  exercitation.</span
            >
              <div
                  class="h-[6px] self-stretch shrink-0 bg-[#fff] relative z-[92]"
              ></div>
            </div>
          </div>
          <div
              class="w-[434px] h-[503.98px] shrink-0 bg-[#1b2335] rounded-[24px] relative z-[93]"
          >
            <div
                class="flex w-[320.241px] flex-col gap-[24px] items-start flex-nowrap relative z-[94] mt-[124px] mr-0 mb-0 ml-[52px]"
            >
              <div
                  class="w-[76px] h-[76px] shrink-0 bg-[url(https://static.codia.ai/image/2025-03-24/393237a6-7300-4c97-9e40-6d64d2e22533.svg)] bg-cover bg-no-repeat relative overflow-hidden z-[95]"
              ></div>
              <span
                  class="h-[44px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[36px] font-bold leading-[44px] text-[#fff] tracking-[-0.72px] relative text-left whitespace-nowrap z-[96]"
              >Motion Graphic</span
              ><span
                class="flex w-[320.241px] h-[79.97px] justify-start items-center self-stretch shrink-0 font-['Lato'] text-[20px] font-normal leading-[24px] text-[#d5d6d9] relative text-left z-[97]"
            >Lorem ipsum dolor sit amet,<br />consectetur adipiscing
                  elit.</span
            >
              <div
                  class="h-[6px] self-stretch shrink-0 bg-[#fff] relative z-[98]"
              ></div>
            </div>
          </div>
          <div
              class="w-[434px] h-[503.98px] shrink-0 bg-[#1b2335] rounded-[24px] relative z-[99]"
          >
            <div
                class="flex w-[320.241px] flex-col gap-[24px] items-start flex-nowrap relative z-[100] mt-[124px] mr-0 mb-0 ml-[52px]"
            >
              <div
                  class="w-[76px] h-[76px] shrink-0 bg-[url(https://static.codia.ai/image/2025-03-24/bc1dd7f8-9413-4aae-a5ed-e438520f29c7.svg)] bg-cover bg-no-repeat relative overflow-hidden z-[101]"
              ></div>
              <span
                  class="h-[44px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[36px] font-bold leading-[44px] text-[#fff] tracking-[-0.72px] relative text-left whitespace-nowrap z-[102]"
              >Motion Graphic</span
              ><span
                class="flex w-[320.241px] h-[79.97px] justify-start items-center self-stretch shrink-0 font-['Lato'] text-[20px] font-normal leading-[24px] text-[#d5d6d9] relative text-left z-[103]"
            >Lorem ipsum dolor sit amet,<br />consectetur adipiscing
                  elit.</span
            >
              <div
                  class="h-[6px] self-stretch shrink-0 bg-[#fff] relative z-[104]"
              ></div>
            </div>
          </div>
        </div>
        <div class="w-[964px] h-[88px] shrink-0 relative z-[105]">
          <div
              class="flex w-[193px] gap-[17px] items-center flex-nowrap relative z-[106] mt-0 mr-0 mb-0 ml-[771px]"
          >
            <div
                class="w-[88px] h-[88px] shrink-0 bg-[#1b2335] rounded-[360px] relative overflow-hidden shadow-[0_2px_12px_0_rgba(20,20,43,0.08)] z-[107]"
            >
              <div
                  class="w-[34.56px] h-[36px] bg-[url(https://static.codia.ai/image/2025-03-24/042a92d1-fa1b-4339-9b0c-b2a15fb05956.svg)] bg-cover bg-no-repeat relative overflow-hidden z-[108] mt-[26px] mr-0 mb-0 ml-[23.72px]"
              ></div>
            </div>
            <div
                class="w-[88px] h-[88px] shrink-0 bg-[#156fee] rounded-[360px] relative overflow-hidden shadow-[0_2px_12px_0_rgba(20,20,43,0.08)] z-[109]"
            >
              <div
                  class="w-[34.56px] h-[36px] bg-[url(https://static.codia.ai/image/2025-03-24/7f23b519-e5db-40c8-957b-0d4740e6f9d9.svg)] bg-cover bg-no-repeat relative overflow-hidden z-[110] mt-[26px] mr-0 mb-0 ml-[29.72px]"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const positions = [
  {
    id: 1,
    title: 'Tech Lead',
    description: 'Leading development teams and architecting scalable solutions. Mentoring developers and driving technical excellence across projects.',
    duration: 'Jan 2022 - Present',
    experience: '5+ years',
    icon: 'heroicons:user-circle'
  },
  {
    id: 2,
    title: 'Senior Full-Stack Developer',
    description: 'Leading development teams and architecting scalable solutions. Mentoring developers and driving technical excellence across projects.',
    duration: 'Mar 2020 - Dec 2021',
    experience: '3-5 years',
    icon: 'heroicons:code-bracket'
  },
  {
    id: 3,
    title: 'Technical Support Engineer',
    description: 'Leading development teams and architecting scalable solutions. Mentoring developers and driving technical excellence across projects.',
    duration: 'Jun 2019 - Feb 2020',
    experience: '2-4 years',
    icon: 'heroicons:wrench-screwdriver'
  },
  {
    id: 4,
    title: 'Frontend Developer',
    description: 'Leading development teams and architecting scalable solutions. Mentoring developers and driving technical excellence across projects.',
    duration: 'Sep 2018 - May 2019',
    experience: '2-3 years',
    icon: 'heroicons:paint-brush'
  },
  {
    id: 5,
    title: 'Backend Developer',
    description: 'Leading development teams and architecting scalable solutions. Mentoring developers and driving technical excellence across projects.',
    duration: 'Jan 2017 - Aug 2018',
    experience: '2-4 years',
    icon: 'heroicons:server'
  },
  {
    id: 6,
    title: 'Junior Developer',
    description: 'Leading development teams and architecting scalable solutions. Mentoring developers and driving technical excellence across projects.',
    duration: 'Jun 2016 - Dec 2016',
    experience: '0-2 years',
    icon: 'heroicons:academic-cap'
  }
]



const itemsPerSlide = ref(4)
const currentSlide = ref(0)

// Responsive items per slide
const updateItemsPerSlide = () => {
  const width = window.innerWidth
  if (width < 640) {
    itemsPerSlide.value = 1
  } else if (width < 1024) {
    itemsPerSlide.value = 2
  } else if (width < 1280) {
    itemsPerSlide.value = 3
  } else {
    itemsPerSlide.value = 4
  }
}

const slideGroups = computed(() => {
  const groups = []
  for (let i = 0; i < positions.length; i += itemsPerSlide.value) {
    groups.push(positions.slice(i, i + itemsPerSlide.value))
  }
  return groups
})

function nextSlide() {
  if (slideGroups.value.length > 1) {
    currentSlide.value = (currentSlide.value + 1) % slideGroups.value.length
  }
}

function prevSlide() {
  if (slideGroups.value.length > 1) {
    currentSlide.value = (currentSlide.value - 1 + slideGroups.value.length) % slideGroups.value.length
  }
}

function goToSlide(index) {
  currentSlide.value = index
}

function selectPosition(position) {
  // You can emit an event or handle position selection here
  console.log('Selected position:', position.title)
}

// Keyboard navigation
const handleKeydown = (event) => {
  if (event.key === 'ArrowLeft') {
    prevSlide()
  } else if (event.key === 'ArrowRight') {
    nextSlide()
  }
}

onMounted(() => {
  updateItemsPerSlide()
  window.addEventListener('resize', updateItemsPerSlide)
  window.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateItemsPerSlide)
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.position-cards-container {
  overflow: hidden;
}
.position-cards-slider {
  display: flex;
  width: 100%;
}

.position-card:focus {
  outline: 2px solid #1570EF;
  outline-offset: 2px;
}

.position-card:hover .w-full {
  background: linear-gradient(90deg, #1570EF 0%, #1a56db 100%);
}

@media (max-width: 640px) {
  .position-card {
    min-height: 380px;
  }
}
</style>
