<template>
  <div class="hero-section bg-[#0a0d12] overflow-hidden">
    <div class="container flex flex-col items-center md:gap-[120px] pt-[80px] pb-[60px] md:py-[120px] px-4 md:px-[120px] max-w-[1600px] mx-auto">
      <!-- Header Section -->
      <PageHeader />
      
      <!-- Hero Content -->
      <div class="hero-content flex flex-col gap-[20px] md:gap-[40px] items-center max-w-full md:max-w-[880px]">
        <div class="hero-title text-center">
          <span class="block font-['Bricolage_Grotesque'] text-[42px] md:text-[90px] font-extralight leading-[50px] md:leading-[96px] text-white">
            First Class Technology
          </span>
          <span class="block font-['Bricolage_Grotesque'] text-[42px] md:text-[90px] font-extralight leading-[50px] md:leading-[108px] text-[#156fee]">
            CONSULTANT
          </span>
        </div>
        
        <span class="hero-subtitle font-['Lato'] text-[16px] md:text-[20px] font-normal leading-[22px] md:leading-[24px] text-[#a3a7ae] text-center max-w-full md:max-w-[497px] px-4 md:px-0">
          I assess technical skills, cultural fit, and long-term potential, ensuring companies hire the best Talents.
        </span>
        
        <div class="cta-primary bg-white rounded-[360px] px-5 md:px-[32px] py-2 md:py-[8px] flex items-center gap-[10px] mt-3 md:mt-0">
          <span class="font-['Lato'] text-[16px] md:text-[18px] font-bold leading-[24px] md:leading-[28px] text-black">Book A Call</span>
          <div class="w-[16px] md:w-[20px] h-[10px] md:h-[12px] bg-[url(https://static.codia.ai/image/2025-03-24/ada8f125-c10b-4429-a9fc-04dca969cf06.svg)] bg-cover bg-no-repeat"></div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import PageHeader from '~/components/layout/PageHeader.vue';
</script>

<style scoped>
@media (max-width: 768px) {
  .service-item {
    @apply border-b border-gray-800 pb-8;
  }
  
  .service-list-item:last-child {
    @apply border-b-0 pb-0;
  }
}
</style>
