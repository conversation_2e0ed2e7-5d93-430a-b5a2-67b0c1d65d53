<template>
  <div class="value-propositions bg-[#0a0d12] overflow-hidden">
    <div class="container max-w-[1600px] mx-auto">
      <!-- First section -->
      <div class="section flex flex-col gap-[64px] justify-center pt-[120px] pr-[120px] pb-[120px] pl-[120px]">
        <div class="content-wrapper">
          <div class="text-wrapper font-['Bricolage_Grotesque'] text-[90px] font-extralight leading-[108px]">
            <span class="text-white">FIND THE RIGHT TEAM<br />WITH</span>
            <span class="text-[#156fee]"> EXPERT</span>
            <span class="text-white"> GUIDANCE</span>
          </div>
        </div>
      </div>

      <!-- Second section -->
      <div class="section flex flex-col gap-[64px] justify-center items-end pt-[120px] pr-[120px] pb-[120px] pl-[120px]">
        <div class="text-wrapper w-[607px]">
          <span class="block font-['Bricolage_Grotesque'] text-[90px] font-extralight leading-[108px] text-white">WHAT I</span>
          <div class="flex gap-[3px] items-center ml-[147px] mt-[-9px]">
            <span v-for="(letter, index) in 'PROMISE'" :key="index"
                  class="font-['Bricolage_Grotesque'] text-[90px] font-extralight leading-[90px] text-[#156fee]">
              {{ letter }}
            </span>
          </div>
          <span class="block font-['Bricolage_Grotesque'] text-[90px] font-extralight leading-[90px] text-white ml-[90px]">YOU WITH</span>
        </div>
      </div>

      <!-- Third section -->
      <div class="section flex flex-col gap-[64px] justify-center pt-[120px] pr-[120px] pb-[120px] pl-[120px]">
        <div class="content-wrapper flex flex-col justify-center items-start self-stretch">
          <div class="text-wrapper font-['Bricolage_Grotesque'] text-[90px] font-extralight leading-[108px]">
            <span class="text-white">CANDIDATE SOURCING <br />AND</span>
            <span class="text-[#156fee]"> EVALUATION</span>
          </div>
        </div>
      </div>

      <!-- Fourth section -->
      <div class="section flex flex-col gap-[64px] justify-center items-end pt-[120px] pr-[120px] pb-[120px] pl-[120px]">
        <div class="content-wrapper flex flex-col justify-center items-end w-[734px]">
          <div class="text-wrapper font-['Bricolage_Grotesque'] text-[90px] font-extralight leading-[108px]">
            <span class="text-white">ADVISORY AND <br /></span>
            <span class="text-[#156fee]">DECISION SUPPORT</span>
          </div>
        </div>
      </div>
      
      <!-- Fifth section -->
      <div class="section flex flex-col gap-[64px] justify-center pt-[120px] pr-[120px] pb-[120px] pl-[120px]">
        <div class="content-wrapper flex flex-col justify-center items-start self-stretch">
          <div class="text-wrapper font-['Bricolage_Grotesque'] text-[90px] font-extralight leading-[108px]">
            <span class="text-white">POST-HIRING <br /></span>
            <span class="text-[#156fee]">SUPPORT</span>
          </div>
          <div class="mt-8">
            <button class="bg-white rounded-full px-8 py-2 text-black font-['Lato'] font-bold">Learn More</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// The component uses pure presentation styling, no additional script needed
</script> 