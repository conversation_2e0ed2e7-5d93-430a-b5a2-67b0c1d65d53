<template>
  <div
      class="flex w-[1600px] pt-[120px] pr-[120px] pb-[120px] pl-[120px] flex-col gap-[64px] justify-center items-center shrink-0 flex-nowrap relative overflow-hidden z-[54] bg-[#0a0d12]"
  >
    <div
        class="flex w-[752px] flex-col gap-[64px] items-center shrink-0 flex-nowrap relative z-[55]"
    >
      <div
          class="flex w-[752px] flex-col gap-[16px] items-center shrink-0 flex-nowrap relative z-[56]"
      >
          <span
              class="flex w-[444px] h-[90px] justify-center items-center shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[72px] font-normal leading-[90px] text-[#fff] tracking-[-1.44px] relative text-center whitespace-nowrap z-[57]"
          >Voices of Trust</span
          ><span
          class="flex w-[752px] h-[48px] justify-center items-center self-stretch shrink-0 font-['Lato'] text-[20px] font-normal leading-[24px] text-[#a3a7ae] relative text-center z-[58]"
      >Explore the testimonials and feedback from our valued customers to
            gain insights into their
          </span>
      </div>
      <div
          class="w-[746px] h-[248px] shrink-0 relative overflow-hidden z-[59]"
      >
        <div
            class="w-[590px] h-[248px] text-[0px] bg-[#1b2335] rounded-[24px] absolute top-0 left-[78px] overflow-hidden z-[60]"
        >
            <span
                class="flex w-[529.58px] h-[112px] justify-start items-center font-['Lato'] text-[16px] font-normal leading-[28px] text-[#fff] relative text-left overflow-hidden z-[67] mt-[27.5px] mr-0 mb-0 ml-[28px]"
            >‘’It was a great experience! This API is perfect to use in my
              projects. <br />Their documentation is very clear and
              comprehensive. The support <br />team is also fast and helpful.
              They accelerated the development <br />process of the project a
              lot. I would definitely recommend it’’</span
            >
          <div
              class="w-[191.159px] h-[56px] relative z-[63] mt-[24px] mr-0 mb-0 ml-[28px]"
          >
            <div
                class="h-[56px] bg-[url(https://static.codia.ai/image/2025-03-24/359e28c5-57bf-4c5c-bcd0-466ebd21e06e.png)] bg-cover bg-no-repeat rounded-[360px] absolute top-0 left-0 right-[135.159px] overflow-hidden z-[64]"
            ></div>
            <div
                class="w-[114.159px] h-[51px] text-[0px] absolute top-px left-[77px] z-[66]"
            >
                <span
                    class="block h-[28px] font-['Lato'] text-[16px] font-normal leading-[28px] text-[#fff] relative text-left whitespace-nowrap z-[65] mt-0 mr-0 mb-0 ml-0"
                >Bessie Cooper</span
                ><span
                class="block h-[20px] font-['Lato'] text-[12px] font-normal leading-[18px] text-[#a3a7ae] relative text-left whitespace-nowrap z-[66] mt-[3px] mr-0 mb-0 ml-0"
            >Software Engineer</span
            >
            </div>
          </div>
          <div
              class="bg-[rgba(255,255,255,0.02)] absolute top-0 bottom-0 left-0 right-0 z-[61]"
          ></div>
          <div
              class="w-full h-full rounded-[16px] border-solid border border-[rgba(255,255,255,0.05)] absolute top-0 left-0 z-[62]"
          ></div>
        </div>
        <div
            class="w-[36px] h-[36px] bg-[rgba(255,255,255,0.02)] rounded-[360px] absolute top-[106px] left-[2px] overflow-hidden shadow-[0_-32px_64px_0_rgba(206,206,251,0.05)_inset] z-[68]"
        >
          <div
              class="w-[28px] h-[28px] bg-[url(https://static.codia.ai/image/2025-03-24/4c5dfd8b-0938-4dbc-9016-fda926f9aa02.svg)] bg-cover bg-no-repeat relative overflow-hidden z-[69] mt-[4px] mr-0 mb-0 ml-[4px]"
          ></div>
          <div
              class="w-full h-full rounded-[99px] border-solid border border-[rgba(255,255,255,0.05)] absolute top-0 left-0 z-[70]"
          ></div>
        </div>
        <div
            class="w-[36px] h-[36px] bg-[rgba(255,255,255,0.02)] rounded-[360px] absolute top-[106px] left-[708px] overflow-hidden shadow-[0_-32px_64px_0_rgba(206,206,251,0.05)_inset] z-[71]"
        >
          <div
              class="w-[28px] h-[28px] bg-[url(https://static.codia.ai/image/2025-03-24/acab9423-a99c-4c1d-a978-9d1c53e8fcf5.svg)] bg-cover bg-no-repeat relative overflow-hidden z-[72] mt-[4px] mr-0 mb-0 ml-[4px]"
          ></div>
          <div
              class="w-full h-full rounded-[99px] border-solid border border-[rgba(255,255,255,0.05)] absolute top-0 left-0 z-[73]"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useState } from '#app';

const testimonials = [0, 1, 2];
const currentTestimonial = useState('testimonialSlide', () => 0);

function nextTestimonial() {
  currentTestimonial.value = (currentTestimonial.value + 100) % (testimonials.length * 100);
}

function prevTestimonial() {
  currentTestimonial.value = (currentTestimonial.value - 100 + (testimonials.length * 100)) % (testimonials.length * 100);
}
</script>

<style scoped>
.testimonials-wrapper {
  overflow: hidden;
  width: 100%;
}

.testimonials-container {
  display: flex;
  width: 100%;
}

.testimonial-slide {
  width: 100%;
}
</style> 