<template>
  <div
      class="flex w-[1600px] pt-0 pr-[120px] pb-[120px] pl-[120px] flex-col gap-[64px] justify-center items-center shrink-0 flex-nowrap relative overflow-hidden z-[158] bg-[#0a0d12]"
  >
    <div class="w-[1232px] h-[961.15px] shrink-0 relative z-[159]">
      <div
          class="w-[968px] h-[624.439px] absolute top-0 left-[132px] z-[160]"
      >
        <div class="w-[560px] h-[359px] absolute top-0 left-0 z-[161]">
          <div
              class="w-[559.396px] h-[305.564px] bg-[url(https://static.codia.ai/image/2025-03-24/294eea8b-0cb4-408f-b922-215adddff58c.svg)] bg-[length:100%_100%] bg-no-repeat relative z-[162] mt-[26.502px] mr-0 mb-0 ml-[0.4px]"
          ></div>
        </div>
        <div
            class="flex w-[646px] h-[196px] flex-col gap-[80px] items-center flex-nowrap absolute top-[167.02px] left-[161px] z-[165]"
        >
          <div class="h-[66px] self-stretch shrink-0 relative z-[166]">
              <span
                  class="flex w-[480px] h-[90px] justify-center items-center font-['Bricolage_Grotesque'] text-[72px] font-normal leading-[90px] text-[#fff] tracking-[-1.44px] absolute top-[-17.94px] left-[calc(50%-239.52px)] text-center whitespace-nowrap z-[167]"
              >Blogs & Insights</span
              >
          </div>
          <div
              class="h-[50px] self-stretch shrink-0 bg-[#1b2335] rounded-[360px] border-solid border border-[#252b37] relative z-[168]"
          >
              <span
                  class="flex w-[526.091px] h-[24px] justify-center items-center font-['Lato'] text-[20px] font-normal leading-[24px] text-[#d5d6d9] absolute top-[12px] left-[calc(50%-263px)] text-center whitespace-nowrap z-[169]"
              >Expert Advice for Building High-Performing Teams</span
              >
          </div>
        </div>
        <div
            class="w-[231px] h-[47.08%] absolute top-[52.92%] right-0 z-[163]"
        >
          <div
              class="w-[230.065px] h-[292.622px] bg-[url(https://static.codia.ai/image/2025-03-24/1a3cb1d6-8eb3-444a-b30b-49dc261f4410.svg)] bg-[length:100%_100%] bg-no-repeat relative z-[164] mt-[0.49px] mr-0 mb-0 ml-[0.39px]"
          ></div>
        </div>
      </div>
      <div
          class="w-[1232px] h-[564px] absolute top-[397.149px] left-0 z-[170]"
      >
        <div
            class="bg-[#1b2335] rounded-[12px] border-solid border border-[rgba(255,255,255,0.05)] absolute top-[-0.27px] bottom-[148.27px] left-0 right-[942px] overflow-hidden z-[171]"
        >
            <span
                class="flex h-[279px] justify-start items-center font-['Bricolage_Grotesque'] text-[232px] font-semibold opacity-[0.03] leading-[278.4px] text-[#fff] tracking-[-4.55px] absolute top-[-61px] left-[-70px] text-left whitespace-nowrap z-[172]"
            >01</span
            >
          <div class="w-[260px] h-[137.59px] absolute top-0 left-0 z-[173]">
            <div
                class="w-[24px] h-[24px] bg-[url(https://static.codia.ai/image/2025-03-24/86f6ba41-3044-44b1-a93c-7b4d634c4206.svg)] bg-cover bg-no-repeat relative z-[174] mt-[24px] mr-0 mb-0 ml-[20px]"
            ></div>
          </div>
          <div
              class="flex w-[202px] h-[60px] flex-col items-start flex-nowrap absolute top-[68.869px] left-[18px] z-[175]"
          >
              <span
                  class="h-[30px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[24px] font-bold leading-[30px] text-[#fff] relative text-left whitespace-nowrap z-[176]"
              >User centric</span
              ><span
              class="h-[30px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[24px] font-normal leading-[30px] text-[#fff] relative text-left whitespace-nowrap z-[177]"
          >design with Figma</span
          >
          </div>
          <div
              class="w-[99.31%] h-[212.67px] absolute bottom-[-1.109px] left-0 z-[178]"
          >
            <div
                class="w-[290px] h-[210.822px] bg-[url(https://static.codia.ai/image/2025-03-24/6e6f3dfa-86ba-4321-949a-1acd6305335d.svg)] bg-[length:100%_100%] bg-no-repeat relative z-[179] mt-[0.85px] mr-0 mb-0 ml-[-1px]"
            ></div>
          </div>
        </div>
        <div
            class="bg-[#1b2335] rounded-[12px] border-solid border border-[rgba(255,255,255,0.05)] absolute top-[20px] bottom-[128px] left-[942px] right-0 overflow-hidden z-[200]"
        >
            <span
                class="flex h-[279px] justify-start items-center font-['Bricolage_Grotesque'] text-[232px] font-semibold opacity-[0.03] leading-[278.4px] text-[#fff] tracking-[-4.55px] absolute top-[-61px] left-[-70px] text-left whitespace-nowrap z-[201]"
            >04</span
            >
          <div class="w-[260px] h-[137.59px] absolute top-0 left-0 z-[202]">
            <div
                class="w-[24px] h-[24px] bg-[url(https://static.codia.ai/image/2025-03-24/af7e669a-6d5c-47e7-bee1-63d35b3e1084.svg)] bg-cover bg-no-repeat relative z-[203] mt-[24px] mr-0 mb-0 ml-[20px]"
            ></div>
            <div
                class="flex w-[202px] flex-col items-start flex-nowrap relative z-[204] mt-[7.869px] mr-0 mb-0 ml-[20px]"
            >
                <span
                    class="h-[30px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[24px] font-bold leading-[30px] text-[#fff] relative text-left whitespace-nowrap z-[205]"
                >User centric</span
                ><span
                class="h-[30px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[24px] font-normal leading-[30px] text-[#fff] relative text-left whitespace-nowrap z-[206]"
            >design with Figma</span
            >
            </div>
          </div>
          <div
              class="w-[99.31%] h-[207.13px] absolute bottom-0 left-0 overflow-hidden z-[207]"
          >
            <div
                class="w-[288px] h-[205.963px] bg-[url(https://static.codia.ai/image/2025-03-24/515114a3-ec48-4b5e-a44b-920c1a10ad13.svg)] bg-[length:100%_100%] bg-no-repeat relative z-[208] mt-[0.41px] mr-0 mb-0 ml-0"
            ></div>
          </div>
        </div>
        <div
            class="bg-[#1b2335] rounded-[12px] border-solid border border-[rgba(255,255,255,0.05)] absolute top-[80px] bottom-[68px] left-[314px] right-[628px] overflow-hidden z-[180]"
        >
            <span
                class="flex h-[279px] justify-start items-center font-['Bricolage_Grotesque'] text-[232px] font-semibold opacity-[0.03] leading-[278.4px] text-[#fff] tracking-[-4.55px] absolute top-[-61px] left-[-70px] text-left whitespace-nowrap z-[181]"
            >02</span
            >
          <div class="w-[260px] h-[137.59px] absolute top-0 left-0 z-[182]">
            <div
                class="w-[24px] h-[24px] bg-[url(https://static.codia.ai/image/2025-03-24/d507b6e6-038b-4189-bb49-87bf0f893419.svg)] bg-cover bg-no-repeat relative z-[183] mt-[24px] mr-0 mb-0 ml-[20px]"
            ></div>
            <div
                class="flex w-[202px] flex-col items-start flex-nowrap relative z-[184] mt-[7.869px] mr-0 mb-0 ml-[20px]"
            >
                <span
                    class="h-[30px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[24px] font-bold leading-[30px] text-[#fff] relative text-left whitespace-nowrap z-[185]"
                >User centric</span
                ><span
                class="h-[30px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[24px] font-normal leading-[30px] text-[#fff] relative text-left whitespace-nowrap z-[186]"
            >design with Figma</span
            >
            </div>
          </div>
          <div
              class="w-[99.31%] h-[212.67px] absolute bottom-[-1.109px] left-0 overflow-hidden z-[187]"
          >
            <div
                class="w-[288px] h-[212.67px] absolute top-0 left-0 overflow-hidden z-[188]"
            >
              <div
                  class="w-[287.991px] h-[212.67px] relative overflow-hidden z-[189] mt-0 mr-0 mb-0 ml-0"
              >
                <div
                    class="w-[287.991px] h-[210.822px] bg-[url(https://static.codia.ai/image/2025-03-24/6bfe2e7d-3c17-4511-8891-14754626908e.svg)] bg-[length:100%_100%] bg-no-repeat relative z-[190] mt-[0.85px] mr-0 mb-0 ml-0"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <div
            class="bg-[#1b2335] rounded-[12px] border-solid border border-[rgba(255,255,255,0.05)] absolute top-[148px] bottom-0 left-[628px] right-[314px] overflow-hidden z-[191]"
        >
            <span
                class="flex h-[279px] justify-start items-center font-['Bricolage_Grotesque'] text-[232px] font-semibold opacity-[0.03] leading-[278.4px] text-[#fff] tracking-[-4.55px] absolute top-[-61px] left-[-70px] text-left whitespace-nowrap z-[192]"
            >03</span
            >
          <div class="w-[260px] h-[137.59px] absolute top-0 left-0 z-[193]">
            <div
                class="w-[24px] h-[24px] bg-[url(https://static.codia.ai/image/2025-03-24/b99ccea2-a9c1-4c70-9a2d-ce3546d71067.svg)] bg-cover bg-no-repeat relative z-[194] mt-[24px] mr-0 mb-0 ml-[20px]"
            ></div>
            <div
                class="flex w-[202px] flex-col items-start flex-nowrap relative z-[195] mt-[7.869px] mr-0 mb-0 ml-[20px]"
            >
                <span
                    class="h-[30px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[24px] font-bold leading-[30px] text-[#fff] relative text-left whitespace-nowrap z-[196]"
                >User centric</span
                ><span
                class="h-[30px] self-stretch shrink-0 basis-auto font-['Bricolage_Grotesque'] text-[24px] font-normal leading-[30px] text-[#fff] relative text-left whitespace-nowrap z-[197]"
            >design with Figma</span
            >
            </div>
          </div>
          <div
              class="w-[99.31%] h-[212.67px] absolute bottom-0 left-0 overflow-hidden z-[198]"
          >
            <div
                class="w-[288px] h-[210.822px] bg-[url(https://static.codia.ai/image/2025-03-24/8530d662-beec-4485-8462-a94269b54334.svg)] bg-[length:100%_100%] bg-no-repeat relative z-[199] mt-[1.092px] mr-0 mb-0 ml-0"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
</style>